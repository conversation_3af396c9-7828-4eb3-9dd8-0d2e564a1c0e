<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>型号数据包看板</title>
    
    <!-- Layui CSS -->
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            overflow-x: hidden;
        }
        
        .dashboard-container {
            width: 100vw;
            height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部区域 */
        .header-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dashboard-title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .filter-controls {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-item label {
            font-size: 16px;
            color: #34495e;
            font-weight: 500;
        }
        
        .layui-form-select {
            min-width: 150px;
        }
        
        /* 修复下拉框z-index问题 */
        .layui-form-select .layui-form-selected dl,
        .layui-form-select dl {
            z-index: 9999 !important;
        }
        
        .header-section {
            z-index: 1000;
            position: relative;
        }
        
        /* 主体区域 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 140px);
        }
        
        .chart-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        
        .chart-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .panel-controls {
            display: flex;
            gap: 10px;
        }
        
        .chart-container {
            height: calc(100% - 50px);
            min-height: 300px;
        }
        
        /* 签署统计特殊样式 */
        .sign-radio {
            display: flex;
            gap: 15px;
        }
        
        .sign-radio input[type="radio"] {
            margin-right: 5px;
        }
        
        .sign-radio label {
            font-size: 14px;
            color: #34495e;
            cursor: pointer;
        }
        
        /* 响应式调整 */
        @media (max-width: 1366px) {
            .dashboard-title {
                font-size: 28px;
            }
            
            .filter-item label {
                font-size: 14px;
            }
            
            .panel-title {
                font-size: 16px;
            }
        }
        
        /* Layer弹窗样式优化 */
        .detail-table {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .layui-table {
            margin: 0;
        }
        
        .layui-table th,
        .layui-table td {
            text-align: center;
            padding: 8px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 顶部区域 -->
        <div class="header-section">
            <div class="dashboard-title">型号数据包看板</div>
            <div class="filter-controls">
                <div class="filter-item">
                    <label>型号：</label>
                    <div class="layui-form">
                        <select id="modelSelect" lay-filter="modelSelect">
                            <option value="">全部型号</option>
                        </select>
                    </div>
                </div>
                <div class="filter-item">
                    <label>专业：</label>
                    <div class="layui-form">
                        <select id="professionSelect" lay-filter="professionSelect">
                            <option value="">全部专业</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主体区域 -->
        <div class="main-content">
            <!-- 第一行 -->
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">原始记录统计</div>
                </div>
                <div class="chart-container" id="originalRecordChart"></div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">影像记录统计</div>
                </div>
                <div class="chart-container" id="imageRecordChart"></div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">质量数据统计</div>
                </div>
                <div class="chart-container" id="qualityDataChart"></div>
            </div>
            
            <!-- 第二行 -->
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">确认表统计</div>
                </div>
                <div class="chart-container" id="confirmTableChart"></div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">全景统计</div>
                </div>
                <div class="chart-container" id="panoramaChart"></div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    <div class="panel-title">签署统计</div>
                    <div class="panel-controls">
                        <div class="sign-radio">
                            <label>
                                <input type="radio" name="signStatus" value="signed" checked>
                                已签署
                            </label>
                            <label>
                                <input type="radio" name="signStatus" value="unsigned">
                                未签署
                            </label>
                        </div>
                    </div>
                </div>
                <div class="chart-container" id="signatureChart"></div>
            </div>
        </div>
    </div>
    
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Layui -->
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    
    <script>
        // 模拟数据
        var mockData = {
            models: [
                { id: 'A001', name: 'A001型号' },
                { id: 'B002', name: 'B002型号' },
                { id: 'C003', name: 'C003型号' },
                { id: 'D004', name: 'D004型号' },
                { id: 'E005', name: 'E005型号' },
                { id: 'F006', name: 'F006型号' },
                { id: 'G007', name: 'G007型号' },
                { id: 'H008', name: 'H008型号' },
                { id: 'I009', name: 'I009型号' },
                { id: 'J010', name: 'J010型号' }
            ],
            professions: {
                'A001': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'elec', name: '电气专业' },
                    { id: 'soft', name: '软件专业' }
                ],
                'B002': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'elec', name: '电气专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'C003': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'soft', name: '软件专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'D004': [
                    { id: 'elec', name: '电气专业' },
                    { id: 'soft', name: '软件专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'E005': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'elec', name: '电气专业' },
                    { id: 'soft', name: '软件专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'F006': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'elec', name: '电气专业' }
                ],
                'G007': [
                    { id: 'soft', name: '软件专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'H008': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'soft', name: '软件专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'I009': [
                    { id: 'elec', name: '电气专业' },
                    { id: 'test', name: '测试专业' }
                ],
                'J010': [
                    { id: 'mech', name: '机械专业' },
                    { id: 'elec', name: '电气专业' },
                    { id: 'soft', name: '软件专业' }
                ]
            },
            processes: {
                'mech': ['加工工艺', '装配工艺', '检验工艺', '包装工艺'],
                'elec': ['电路设计', '电气测试', '电气装配', '电气调试'],
                'soft': ['需求分析', '代码开发', '单元测试', '集成测试'],
                'test': ['功能测试', '性能测试', '环境测试', '可靠性测试']
            },
            qualityDataTypes: ['检验记录', '测试报告', '校准证书', '不合格品记录', '纠正预防措施']
        };
        
        // 生成随机数据
        function generateRandomData(categories, min, max) {
            return categories.map(function(category) {
                return {
                    name: typeof category === 'object' ? category.name : category,
                    value: Math.floor(Math.random() * (max - min + 1)) + min
                };
            }).sort(function(a, b) { return b.value - a.value; });
        }
        
        // 当前筛选状态
        var currentFilter = {
            model: '',
            profession: ''
        };
        
        // 图表实例
        var charts = {};
        
        // 初始化Layui
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 初始化型号下拉框
            var modelSelect = document.getElementById('modelSelect');
            mockData.models.forEach(function(model) {
                var option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                modelSelect.appendChild(option);
            });
            
            // 型号选择监听
            form.on('select(modelSelect)', function(data){
                currentFilter.model = data.value;
                updateProfessionSelect();
                updateAllCharts();
            });
            
            // 专业选择监听
            form.on('select(professionSelect)', function(data){
                currentFilter.profession = data.value;
                updateAllCharts();
            });
            
            // 签署状态监听
            document.addEventListener('change', function(e) {
                if (e.target.name === 'signStatus') {
                    updateSignatureChart();
                }
            });
            
            form.render();
        });
        
        // 更新专业下拉框
        function updateProfessionSelect() {
            var professionSelect = document.getElementById('professionSelect');
            professionSelect.innerHTML = '<option value="">全部专业</option>';
            
            if (currentFilter.model && mockData.professions[currentFilter.model]) {
                mockData.professions[currentFilter.model].forEach(function(profession) {
                    var option = document.createElement('option');
                    option.value = profession.id;
                    option.textContent = profession.name;
                    professionSelect.appendChild(option);
                });
            }
            
            currentFilter.profession = '';
            layui.form.render('select');
        }
        
        // 获取图表横坐标数据
        function getXAxisData(chartType) {
            if (!currentFilter.model) {
                // 全部型号 → 横坐标为型号
                return mockData.models;
            } else if (!currentFilter.profession) {
                // 选定型号 → 横坐标为专业
                return mockData.professions[currentFilter.model] || [];
            } else {
                // 选定型号+专业 → 横坐标为过程
                var processes = mockData.processes[currentFilter.profession] || [];
                return processes.map(function(process) {
                    return { id: process, name: process };
                });
            }
        }
        
        // 创建基础柱状图配置
        function createBarChartOption(title, data, color) {
            var xAxisData = data.map(function(item) { return item.name; });
            var seriesData = data.map(function(item) { return item.value; });
            
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return params[0].name + '<br/>' + 
                               params[0].seriesName + ': ' + params[0].value;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: xAxisData,
                    axisLabel: {
                        rotate: data.length > 8 ? 45 : 0,
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 12
                    }
                },
                dataZoom: data.length > 8 ? [{
                    type: 'slider',
                    start: 0,
                    end: Math.min(100, (8 / data.length) * 100),
                    height: 20,
                    bottom: '5%'
                }] : [],
                series: [{
                    name: title,
                    type: 'bar',
                    data: seriesData,
                    itemStyle: {
                        color: color,
                        borderRadius: [4, 4, 0, 0]
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowOffsetY: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
        }
        
        // 更新所有图表
        function updateAllCharts() {
            updateOriginalRecordChart();
            updateImageRecordChart();
            updateQualityDataChart();
            updateConfirmTableChart();
            updatePanoramaChart();
            updateSignatureChart();
        }
        
        // 更新原始记录统计图表
        function updateOriginalRecordChart() {
            var xAxisData = getXAxisData('original');
            var data = generateRandomData(xAxisData, 50, 500);
            var option = createBarChartOption('原始记录统计', data, '#3498db');
            
            if (!charts.originalRecord) {
                charts.originalRecord = echarts.init(document.getElementById('originalRecordChart'));
                charts.originalRecord.on('click', function(params) {
                    showDetailModal('原始记录明细', generateDetailData());
                });
            }
            
            charts.originalRecord.setOption(option);
        }
        
        // 更新影像记录统计图表
        function updateImageRecordChart() {
            var xAxisData = getXAxisData('image');
            var data = generateRandomData(xAxisData, 20, 200);
            var option = createBarChartOption('影像记录统计', data, '#e74c3c');
            
            if (!charts.imageRecord) {
                charts.imageRecord = echarts.init(document.getElementById('imageRecordChart'));
                charts.imageRecord.on('click', function(params) {
                    showDetailModal('影像记录明细', generateDetailData());
                });
            }
            
            charts.imageRecord.setOption(option);
        }
        
        // 更新质量数据统计图表
        function updateQualityDataChart() {
            var xAxisData = getXAxisData('quality');
            var data = generateRandomData(xAxisData, 30, 300);
            var option = createBarChartOption('质量数据统计', data, '#f39c12');
            
            if (!charts.qualityData) {
                charts.qualityData = echarts.init(document.getElementById('qualityDataChart'));
                charts.qualityData.on('click', function(params) {
                    // 特殊交互：当选定型号+专业时，显示二级图表
                    if (currentFilter.model && currentFilter.profession) {
                        showSecondaryQualityChart(params.name);
                    } else {
                        showDetailModal('质量数据明细', generateDetailData());
                    }
                });
            }
            
            charts.qualityData.setOption(option);
        }
        
        // 更新确认表统计图表
        function updateConfirmTableChart() {
            var xAxisData = getXAxisData('confirm');
            var data = generateRandomData(xAxisData, 10, 100);
            var option = createBarChartOption('确认表统计', data, '#9b59b6');
            
            if (!charts.confirmTable) {
                charts.confirmTable = echarts.init(document.getElementById('confirmTableChart'));
                charts.confirmTable.on('click', function(params) {
                    showDetailModal('确认表明细', generateDetailData());
                });
            }
            
            charts.confirmTable.setOption(option);
        }
        
        // 更新全景统计图表
        function updatePanoramaChart() {
            var xAxisData = getXAxisData('panorama');
            var data = generateRandomData(xAxisData, 5, 50);
            var option = createBarChartOption('全景统计', data, '#1abc9c');
            
            if (!charts.panorama) {
                charts.panorama = echarts.init(document.getElementById('panoramaChart'));
                charts.panorama.on('click', function(params) {
                    showDetailModal('全景明细', generateDetailData());
                });
            }
            
            charts.panorama.setOption(option);
        }
        
        // 更新签署统计图表
        function updateSignatureChart() {
            var signStatus = document.querySelector('input[name="signStatus"]:checked').value;
            var xAxisData = getXAxisData('signature');
            var data = generateRandomData(xAxisData, 
                signStatus === 'signed' ? 80 : 20, 
                signStatus === 'signed' ? 200 : 80
            );
            var option = createBarChartOption(
                signStatus === 'signed' ? '已签署统计' : '未签署统计', 
                data, 
                signStatus === 'signed' ? '#27ae60' : '#e67e22'
            );
            
            if (!charts.signature) {
                charts.signature = echarts.init(document.getElementById('signatureChart'));
                charts.signature.on('click', function(params) {
                    showDetailModal('签署明细', generateDetailData());
                });
            }
            
            charts.signature.setOption(option);
        }
        
        // 显示二级质量数据图表
        function showSecondaryQualityChart(processName) {
            var data = generateRandomData(mockData.qualityDataTypes, 10, 100);
            var option = createBarChartOption('', data, '#f39c12');
            
            // 为二级图表添加标题
            option.title = {
                text: '质量数据类型统计 - ' + processName,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#2c3e50'
                }
            };
            
            layui.use('layer', function(){
                var layer = layui.layer;
                
                layer.open({
                    type: 1,
                    title: '质量数据详细统计',
                    area: ['800px', '600px'],
                    content: '<div id="secondaryQualityChart" style="width: 100%; height: 500px;"></div>',
                    success: function(layero, index) {
                        var secondaryChart = echarts.init(document.getElementById('secondaryQualityChart'));
                        secondaryChart.setOption(option);
                        
                        // 二级图表点击事件
                        secondaryChart.on('click', function(params) {
                            layer.close(index);
                            showDetailModal('质量数据明细 - ' + params.name, generateDetailData());
                        });
                        
                        // 窗口大小调整时重绘图表
                        window.addEventListener('resize', function() {
                            secondaryChart.resize();
                        });
                    }
                });
            });
        }
        
        // 生成明细数据
        function generateDetailData() {
            var detailData = [];
            for (var i = 1; i <= 20; i++) {
                detailData.push({
                    序号: i,
                    名称: '记录项目' + i,
                    数量: Math.floor(Math.random() * 100) + 1,
                    状态: Math.random() > 0.5 ? '正常' : '异常',
                    更新时间: '2024-01-' + String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')
                });
            }
            return detailData;
        }
        
        // 显示明细弹窗
        function showDetailModal(title, data) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                var tableHtml = '<div class="detail-table"><table class="layui-table">';
                tableHtml += '<thead><tr>';
                
                // 表头
                if (data.length > 0) {
                    Object.keys(data[0]).forEach(function(key) {
                        tableHtml += '<th>' + key + '</th>';
                    });
                }
                tableHtml += '</tr></thead><tbody>';
                
                // 表体
                data.forEach(function(row) {
                    tableHtml += '<tr>';
                    Object.values(row).forEach(function(value) {
                        tableHtml += '<td>' + value + '</td>';
                    });
                    tableHtml += '</tr>';
                });
                
                tableHtml += '</tbody></table></div>';
                
                layer.open({
                    type: 1,
                    title: title,
                    area: ['80%', '70%'],
                    content: tableHtml
                });
            });
        }
        
        // 窗口大小调整时重绘所有图表
        window.addEventListener('resize', function() {
            Object.keys(charts).forEach(function(key) {
                if (charts[key]) {
                    charts[key].resize();
                }
            });
        });
        
        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                updateAllCharts();
            }, 100);
        });
    </script>
</body>
</html>
