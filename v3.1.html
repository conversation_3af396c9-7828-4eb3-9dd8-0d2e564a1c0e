<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>高级天气展示卡片</title>
  <style>
    :root {
      --card-bg: rgba(30, 32, 40, 0.7);
      --card-blur: 18px;
      --card-radius: 28px;
      --card-shadow: 0 8px 32px 0 rgba(0,0,0,0.37);
      --transition: 0.7s cubic-bezier(.4,0,.2,1);
      --primary: #6ec1e4;
      --accent: #f7b733;
      --rain: #3a506b;
      --snow: #b5c6e0;
      --wind: #7f8fa6;
      --text-main: #fff;
      --text-sub: #b0b8c1;
      --btn-bg: rgba(40,42,54,0.7);
      --btn-hover: #22242c;
      --btn-active: #6ec1e4;
    }
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background: #181a20;
      color: var(--text-main);
      font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans', <PERSON><PERSON>, sans-serif;
      overflow: hidden;
    }
    body {
      min-height: 100vh;
      min-width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .bg-anim {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      width: 100vw; height: 100vh;
      z-index: 0;
      pointer-events: none;
      opacity: 1;
      transition: opacity var(--transition);
    }
    .bg-anim.hide {
      opacity: 0;
      pointer-events: none;
    }
    .weather-card {
      position: relative;
      z-index: 2;
      min-width: 320px;
      max-width: 90vw;
      width: 370px;
      min-height: 340px;
      margin: 0 auto;
      background: var(--card-bg);
      border-radius: var(--card-radius);
      box-shadow: var(--card-shadow);
      backdrop-filter: blur(var(--card-blur));
      -webkit-backdrop-filter: blur(var(--card-blur));
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 36px 28px 24px 28px;
      box-sizing: border-box;
      transition: background var(--transition), box-shadow var(--transition);
    }
    .city {
      font-size: 1.3rem;
      font-weight: 600;
      letter-spacing: 1px;
      color: var(--text-main);
      margin-bottom: 8px;
      text-shadow: 0 2px 8px rgba(0,0,0,0.18);
    }
    .temp {
      font-size: 3.2rem;
      font-weight: 700;
      margin: 0 0 8px 0;
      color: var(--accent);
      text-shadow: 0 2px 12px rgba(0,0,0,0.18);
    }
    .desc {
      font-size: 1.1rem;
      color: var(--text-sub);
      margin-bottom: 18px;
      letter-spacing: 1px;
      text-shadow: 0 1px 6px rgba(0,0,0,0.12);
    }
    .weather-icons {
      width: 80px;
      height: 80px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .controls {
      display: flex;
      gap: 18px;
      margin-top: 18px;
    }
    .weather-btn {
      background: var(--btn-bg);
      border: none;
      outline: none;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background var(--transition), box-shadow var(--transition), transform 0.18s;
      box-shadow: 0 2px 8px 0 rgba(0,0,0,0.18);
      font-size: 1.5rem;
      color: var(--text-main);
      position: relative;
      user-select: none;
    }
    .weather-btn:hover {
      background: var(--btn-hover);
      color: var(--accent);
      transform: scale(1.08);
      box-shadow: 0 4px 16px 0 rgba(0,0,0,0.22);
    }
    .weather-btn.active {
      background: var(--btn-active);
      color: #fff;
      box-shadow: 0 6px 24px 0 rgba(110,193,228,0.18);
      transform: scale(1.12);
    }
    @media (max-width: 600px) {
      .weather-card {
        min-width: 90vw;
        width: 98vw;
        min-height: 260px;
        padding: 22px 8vw 18px 8vw;
      }
      .weather-icons {
        width: 60px;
        height: 60px;
      }
      .controls {
        gap: 10px;
      }
    }
  </style>
</head>
<body>
  <!-- 动态背景层 -->
  <canvas id="bg-sunny" class="bg-anim"></canvas>
  <canvas id="bg-rainy" class="bg-anim hide"></canvas>
  <canvas id="bg-snowy" class="bg-anim hide"></canvas>
  <canvas id="bg-windy" class="bg-anim hide"></canvas>

  <div class="weather-card">
    <div class="city" id="city">北京</div>
    <div class="weather-icons" id="weather-icon">
      <!-- SVG图标由JS动态插入 -->
    </div>
    <div class="temp" id="temp">25°C</div>
    <div class="desc" id="desc">局部多云</div>
    <div class="controls">
      <button class="weather-btn active" data-mode="sunny" title="晴天" id="btn-sunny">☀️</button>
      <button class="weather-btn" data-mode="rainy" title="雨天" id="btn-rainy">🌧️</button>
      <button class="weather-btn" data-mode="snowy" title="下雪" id="btn-snowy">❄️</button>
      <button class="weather-btn" data-mode="windy" title="大风" id="btn-windy">💨</button>
    </div>
  </div>

  <script>
    // 天气模式配置
    const weatherModes = {
      sunny: {
        city: '北京',
        temp: '25°C',
        desc: '局部多云',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none"><circle cx="40" cy="40" r="18" fill="#f7b733"/><g><ellipse cx="60" cy="50" rx="12" ry="8" fill="#fff" fill-opacity=".7"/><ellipse cx="50" cy="54" rx="10" ry="7" fill="#fff" fill-opacity=".5"/></g></svg>`,
        bg: 'bg-sunny',
        color: 'var(--accent)',
        cardBg: 'rgba(30, 32, 40, 0.7)'
      },
      rainy: {
        city: '上海',
        temp: '18°C',
        desc: '小雨转阴',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none"><ellipse cx="50" cy="50" rx="16" ry="10" fill="#b0b8c1"/><ellipse cx="38" cy="54" rx="12" ry="8" fill="#fff" fill-opacity=".7"/><g><rect x="38" y="62" width="4" height="12" rx="2" fill="#6ec1e4"/><rect x="48" y="66" width="4" height="10" rx="2" fill="#6ec1e4"/><rect x="56" y="62" width="4" height="12" rx="2" fill="#6ec1e4"/></g></svg>`,
        bg: 'bg-rainy',
        color: 'var(--rain)',
        cardBg: 'rgba(40, 44, 54, 0.7)'
      },
      snowy: {
        city: '哈尔滨',
        temp: '-5°C',
        desc: '大雪纷飞',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none"><ellipse cx="50" cy="50" rx="16" ry="10" fill="#b5c6e0"/><ellipse cx="38" cy="54" rx="12" ry="8" fill="#fff" fill-opacity=".7"/><g><circle cx="44" cy="68" r="3" fill="#fff"/><circle cx="54" cy="72" r="2.5" fill="#fff"/><circle cx="60" cy="68" r="2" fill="#fff"/></g></svg>`,
        bg: 'bg-snowy',
        color: 'var(--snow)',
        cardBg: 'rgba(44, 48, 60, 0.7)'
      },
      windy: {
        city: '呼和浩特',
        temp: '12°C',
        desc: '大风蓝色预警',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none"><ellipse cx="50" cy="50" rx="16" ry="10" fill="#7f8fa6"/><ellipse cx="38" cy="54" rx="12" ry="8" fill="#fff" fill-opacity=".7"/><g><path d="M38 68 Q50 62 62 68" stroke="#6ec1e4" stroke-width="3" fill="none" stroke-linecap="round"/><path d="M44 74 Q54 70 64 74" stroke="#6ec1e4" stroke-width="2" fill="none" stroke-linecap="round"/></g></svg>`,
        bg: 'bg-windy',
        color: 'var(--wind)',
        cardBg: 'rgba(36, 40, 50, 0.7)'
      }
    };

    // 当前模式
    let currentMode = 'sunny';
    const card = document.querySelector('.weather-card');
    const city = document.getElementById('city');
    const temp = document.getElementById('temp');
    const desc = document.getElementById('desc');
    const icon = document.getElementById('weather-icon');
    const btns = document.querySelectorAll('.weather-btn');
    const bgCanvases = {
      sunny: document.getElementById('bg-sunny'),
      rainy: document.getElementById('bg-rainy'),
      snowy: document.getElementById('bg-snowy'),
      windy: document.getElementById('bg-windy')
    };

    // 切换天气模式
    function switchWeather(mode) {
      if (mode === currentMode) return;
      // 按钮激活态
      btns.forEach(btn => btn.classList.toggle('active', btn.dataset.mode === mode));
      // 卡片内容淡出
      card.style.opacity = 0;
      setTimeout(() => {
        // 切换内容
        city.textContent = weatherModes[mode].city;
        temp.textContent = weatherModes[mode].temp;
        desc.textContent = weatherModes[mode].desc;
        icon.innerHTML = weatherModes[mode].icon;
        card.style.background = weatherModes[mode].cardBg;
        temp.style.color = weatherModes[mode].color;
        // 背景切换
        Object.keys(bgCanvases).forEach(k => {
          bgCanvases[k].classList.toggle('hide', k !== mode);
        });
        // 卡片内容淡入
        setTimeout(() => {
          card.style.opacity = 1;
        }, 80);
        currentMode = mode;
      }, 320);
    }
    btns.forEach(btn => {
      btn.addEventListener('click', () => switchWeather(btn.dataset.mode));
    });

    // 初始内容
    icon.innerHTML = weatherModes.sunny.icon;
    temp.style.color = weatherModes.sunny.color;
    card.style.background = weatherModes.sunny.cardBg;

    // 动态背景动画实现
    // 1. 晴天：太阳+缓慢云朵
    function sunnyAnim(ctx, w, h, t) {
      ctx.clearRect(0,0,w,h);
      // 渐变天空
      let grad = ctx.createLinearGradient(0,0,0,h);
      grad.addColorStop(0, '#23243a');
      grad.addColorStop(1, '#6ec1e4');
      ctx.fillStyle = grad;
      ctx.fillRect(0,0,w,h);
      // 太阳
      ctx.save();
      ctx.globalAlpha = 0.7;
      ctx.beginPath();
      ctx.arc(w*0.8, h*0.22, 60, 0, 2*Math.PI);
      ctx.fillStyle = '#f7b733';
      ctx.shadowColor = '#f7b733';
      ctx.shadowBlur = 40;
      ctx.fill();
      ctx.restore();
      // 云朵
      for(let i=0;i<2;i++) {
        let cx = w*0.3 + Math.sin(t/1800+i)*w*0.08;
        let cy = h*0.32 + Math.cos(t/2200+i)*h*0.03;
        ctx.save();
        ctx.globalAlpha = 0.7;
        ctx.beginPath();
        ctx.ellipse(cx, cy, 70, 28, 0, 0, 2*Math.PI);
        ctx.fillStyle = '#fff';
        ctx.shadowColor = '#fff';
        ctx.shadowBlur = 18;
        ctx.fill();
        ctx.restore();
      }
    }
    // 2. 雨天：下落雨滴
    let rainDrops = [];
    function initRain(w, h) {
      rainDrops = Array.from({length: 36}, () => ({
        x: Math.random()*w,
        y: Math.random()*h,
        l: 18+Math.random()*18,
        v: 2+Math.random()*3
      }));
    }
    function rainAnim(ctx, w, h, t) {
      ctx.clearRect(0,0,w,h);
      ctx.fillStyle = '#23243a';
      ctx.fillRect(0,0,w,h);
      ctx.save();
      ctx.globalAlpha = 0.7;
      ctx.fillStyle = '#3a506b';
      ctx.fillRect(0,0,w,h);
      ctx.restore();
      ctx.strokeStyle = '#6ec1e4';
      ctx.lineWidth = 2.2;
      rainDrops.forEach(d => {
        ctx.beginPath();
        ctx.moveTo(d.x, d.y);
        ctx.lineTo(d.x, d.y+d.l);
        ctx.stroke();
        d.y += d.v;
        if (d.y > h) {
          d.x = Math.random()*w;
          d.y = -d.l;
        }
      });
    }
    // 3. 雪天：飘落雪花
    let snowFlakes = [];
    function initSnow(w, h) {
      snowFlakes = Array.from({length: 28}, () => ({
        x: Math.random()*w,
        y: Math.random()*h,
        r: 2+Math.random()*3,
        v: 0.6+Math.random()*1.2,
        drift: 0.5+Math.random()*1.2,
        phase: Math.random()*Math.PI*2
      }));
    }
    function snowAnim(ctx, w, h, t) {
      ctx.clearRect(0,0,w,h);
      let grad = ctx.createLinearGradient(0,0,0,h);
      grad.addColorStop(0, '#23243a');
      grad.addColorStop(1, '#b5c6e0');
      ctx.fillStyle = grad;
      ctx.fillRect(0,0,w,h);
      snowFlakes.forEach(f => {
        ctx.save();
        ctx.globalAlpha = 0.7;
        ctx.beginPath();
        ctx.arc(f.x + Math.sin(t/1200+f.phase)*f.drift*8, f.y, f.r, 0, 2*Math.PI);
        ctx.fillStyle = '#fff';
        ctx.shadowColor = '#fff';
        ctx.shadowBlur = 8;
        ctx.fill();
        ctx.restore();
        f.y += f.v;
        if (f.y > h) {
          f.x = Math.random()*w;
          f.y = -f.r;
        }
      });
    }
    // 4. 大风：树叶/粒子随风
    let windLeaves = [];
    function initWind(w, h) {
      windLeaves = Array.from({length: 18}, () => ({
        x: Math.random()*w,
        y: Math.random()*h,
        r: 8+Math.random()*8,
        v: 2+Math.random()*2,
        drift: 1+Math.random()*2,
        phase: Math.random()*Math.PI*2,
        color: Math.random()>0.5 ? '#6ec1e4' : '#7f8fa6'
      }));
    }
    function windAnim(ctx, w, h, t) {
      ctx.clearRect(0,0,w,h);
      let grad = ctx.createLinearGradient(0,0,w,h);
      grad.addColorStop(0, '#23243a');
      grad.addColorStop(1, '#7f8fa6');
      ctx.fillStyle = grad;
      ctx.fillRect(0,0,w,h);
      windLeaves.forEach(f => {
        ctx.save();
        ctx.globalAlpha = 0.7;
        ctx.beginPath();
        ctx.ellipse(f.x, f.y, f.r, f.r*0.5, Math.sin(t/800+f.phase)*1.2, 0, 2*Math.PI);
        ctx.fillStyle = f.color;
        ctx.shadowColor = f.color;
        ctx.shadowBlur = 8;
        ctx.fill();
        ctx.restore();
        f.x += f.v + Math.sin(t/900+f.phase)*f.drift;
        f.y += Math.sin(t/1200+f.phase)*1.2;
        if (f.x > w+f.r) {
          f.x = -f.r;
          f.y = Math.random()*h;
        }
      });
    }
    // 动画主循环
    function resizeAll() {
      const w = window.innerWidth, h = window.innerHeight;
      Object.values(bgCanvases).forEach(c => {
        c.width = w; c.height = h;
      });
      initRain(w, h);
      initSnow(w, h);
      initWind(w, h);
    }
    window.addEventListener('resize', resizeAll);
    resizeAll();
    function animate() {
      const t = Date.now();
      sunnyAnim(bgCanvases.sunny.getContext('2d'), bgCanvases.sunny.width, bgCanvases.sunny.height, t);
      rainAnim(bgCanvases.rainy.getContext('2d'), bgCanvases.rainy.width, bgCanvases.rainy.height, t);
      snowAnim(bgCanvases.snowy.getContext('2d'), bgCanvases.snowy.width, bgCanvases.snowy.height, t);
      windAnim(bgCanvases.windy.getContext('2d'), bgCanvases.windy.width, bgCanvases.windy.height, t);
      requestAnimationFrame(animate);
    }
    animate();
  </script>
</body>
</html>
