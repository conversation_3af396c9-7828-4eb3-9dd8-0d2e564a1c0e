<!DOCTYPE html><html lang="zh-CN"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover"/><title>高级天气卡片</title><meta name="author" content="wanghq"/>
<style>
*{box-sizing:border-box}html,body{height:100%}body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue",Arial;color:#e6e8eb;background:#0b0f14;overflow:hidden;transition:background .5s ease}
:root{--accent:#64b5ff;--bg1:#0b0f14;--bg2:#101621;--card:#0f141d99;--card-b:#1c2533;--text:#e6e8eb;--muted:#9aa4b2;--glass:blur(16px) saturate(120%);--ease:cubic-bezier(.2,.7,.2,1);--shadow:0 10px 30px rgba(0,0,0,.35)}
body[data-mode="sunny"]{--accent:#ffd166;--bg1:#0b0f14;--bg2:#142233}
body[data-mode="rainy"]{--accent:#64b5ff;--bg1:#0a0d12;--bg2:#0e141e}
body[data-mode="snowy"]{--accent:#a3e4ff;--bg1:#0b0e14;--bg2:#131a26}
body[data-mode="windy"]{--accent:#7bd389;--bg1:#0a0f12;--bg2:#0f171f}
.bg-gradient,canvas#bg{position:fixed;inset:0;width:100%;height:100%;z-index:-2}
.bg-gradient{background:radial-gradient(1200px 1200px at 80% 20%,color-mix(in oklab,var(--accent),#000 70%),transparent 60%),linear-gradient(160deg,var(--bg1),var(--bg2));transition:background .6s var(--ease)}
.wrap{position:relative;display:grid;place-items:center;min-height:100%;padding:4vmin}
.weather-card{width:min(92vw,520px);border-radius:22px;background:var(--card);backdrop-filter:var(--glass);-webkit-backdrop-filter:var(--glass);border:1px solid rgba(255,255,255,.08);box-shadow:var(--shadow);color:var(--text);transition:transform .4s var(--ease),background .4s var(--ease),border-color .4s var(--ease)}
.weather-card:hover{transform:translateY(-2px)}
.card-inner{padding:28px}
.topline{display:flex;align-items:baseline;gap:14px}
.temp{font-size:56px;line-height:1;font-weight:700;letter-spacing:.5px}
.desc{font-size:18px;color:var(--muted)}
.city{margin-top:8px;font-size:14px;color:var(--muted)}
.controls{margin-top:20px;display:grid;grid-template-columns:repeat(4,1fr);gap:10px}
.controls button{appearance:none;border:1px solid rgba(255,255,255,.08);background:linear-gradient(180deg,rgba(255,255,255,.06),rgba(0,0,0,.1));color:var(--text);border-radius:14px;padding:12px 10px;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:8px;transition:all .25s var(--ease);box-shadow:0 2px 10px rgba(0,0,0,.25)}
.controls button svg{width:20px;height:20px;fill:currentColor}
.controls button:hover{border-color:color-mix(in oklab,var(--accent),#fff 10%);box-shadow:0 6px 18px color-mix(in oklab,var(--accent),#000 80%);transform:translateY(-1px)}
.controls button[aria-pressed="true"],.controls button:active{background:linear-gradient(180deg,color-mix(in oklab,var(--accent),#fff 10%),color-mix(in oklab,var(--accent),#000 30%));border-color:color-mix(in oklab,var(--accent),#000 20%)}
.badges{margin-top:16px;display:flex;gap:8px;flex-wrap:wrap}
.badges .chip{font-size:12px;color:var(--muted);border:1px solid rgba(255,255,255,.08);padding:6px 10px;border-radius:999px;background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(0,0,0,.08))}
.footer{margin-top:18px;font-size:12px;color:#7b8696}
@media (max-width:480px){.temp{font-size:48px}.card-inner{padding:20px}.controls button{padding:10px}}
</style></head>
<body data-mode="sunny">
<canvas id="bg"></canvas><div class="bg-gradient"></div>
<main class="wrap"><section class="weather-card" role="region" aria-label="天气信息"><div class="card-inner">
<div class="topline"><div class="temp" id="temp">25°C</div><div class="desc" id="desc">局部多云</div></div>
<div class="city" id="city">上海 · China</div>
<div class="badges"><div class="chip">深色模式</div><div class="chip">玻璃拟态</div><div class="chip">零依赖</div></div>
<div class="controls" role="group" aria-label="切换天气">
<button data-mode="sunny" aria-pressed="true" title="晴天"><svg viewBox="0 0 24 24" aria-hidden="true"><path d="M12 4a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0V5a1 1 0 0 1 1-1Zm0 12a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm7-5a1 1 0 0 1 1 1 1 1 0 1 1-2 0 1 1 0 0 1 1-1Zm-7 8a1 1 0 0 1 1-1h0a1 1 0 1 1-2 0h0a1 1 0 0 1 1 1ZM4 12a1 1 0 0 1 1-1h1a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1Zm12.95 5.536a1 1 0 0 1 1.414 0l.707.707a1 1 0 1 1-1.414 1.414l-.707-.707a1 1 0 0 1 0-1.414ZM4.929 5.636a1 1 0 0 1 1.414-1.414l.707.707A1 1 0 0 1 5.636 6.343l-.707-.707Zm12.728-1.414a1 1 0 0 1 1.414 1.414l-.707.707a1 1 0 0 1-1.414-1.414l.707-.707ZM5.636 17.657a1 1 0 0 1 1.414 0l.707.707a1 1 0 1 1-1.414 1.414l-.707-.707a1 1 0 0 1 0-1.414Z"/></svg><span>晴天</span></button>
<button data-mode="rainy" aria-pressed="false" title="雨天"><svg viewBox="0 0 24 24" aria-hidden="true"><path d="M7 18c0-.552.448-1 1-1s1 .448 1 1-.448 2-1 2-1-1.448-1-2Zm4 0c0-.552.448-1 1-1s1 .448 1 1-.448 2-1 2-1-1.448-1-2Zm4 0c0-.552.448-1 1-1s1 .448 1 1-.448 2-1 2-1-1.448-1-2ZM6 10a5 5 0 0 1 9.584-2.001A4 4 0 1 1 18 14H8a4 4 0 0 1-2-4Z"/></svg><span>雨天</span></button>
<button data-mode="snowy" aria-pressed="false" title="下雪"><svg viewBox="0 0 24 24" aria-hidden="true"><path d="M11 2h2v4l2.5-1.5 1 1.732L14 8l2.5 1.768-1 1.732L13 10v4l2.5-1.5 1 1.732L13 16v4h-2v-4l-2.5 1.5-1-1.732L11 14v-4L8.5 11.5l-1-1.732L11 8 8.5 6.232l1-1.732L11 6V2Z"/></svg><span>下雪</span></button>
<button data-mode="windy" aria-pressed="false" title="大风"><svg viewBox="0 0 24 24" aria-hidden="true"><path d="M3 8h10a3 3 0 1 0-3-3h2a1 1 0 1 1 1 1H3v2Zm0 4h14a3 3 0 1 1-3 3h-2a1 1 0 1 0-1-1H3v-2Zm0 4h7a2 2 0 1 1-2 2H6a1 1 0 1 0-1-1H3v-1Z"/></svg><span>大风</span></button>
</div>
<div class="footer">提示：点击上方按钮在四种天气之间切换，背景动效即时切换并平滑过渡。</div>
</div></section></main>
<script>
(()=>{"use strict";const W=window,D=document,B=D.body,cv=D.getElementById("bg"),ctx=cv.getContext("2d"),dpr=W.devicePixelRatio||1;let Ww=0,Wh=0;function fit(){const r=W.devicePixelRatio||1;cv.width=Math.floor(W.innerWidth*r);cv.height=Math.floor(W.innerHeight*r);cv.style.width=W.innerWidth+"px";cv.style.height=W.innerHeight+"px";ctx.setTransform(r,0,0,r,0,0);Ww=W.innerWidth;Wh=W.innerHeight}fit();W.addEventListener("resize",fit,{passive:!0});
const modes=["sunny","rainy","snowy","windy"];const labels={sunny:"晴天 · 明亮与通透",rainy:"雨天 · 连绵与宁静",snowy:"下雪 · 轻盈与优雅",windy:"大风 · 流动与力量"};const descEl=D.getElementById("desc");const tempEl=D.getElementById("temp");
let cur={mode:"sunny",parts:[],draw:null},prev=null,fadeT=1,fadeStart=0,fadeDur=500;
function rand(a,b){return Math.random()*(b-a)+a}
function build(mode){if(mode==="sunny"){const clouds=[...Array(6)].map(()=>({x:rand(0,Ww),y:rand(20,Wh*.5),r:rand(20,60),s:rand(.1,.4),o:rand(.08,.18)}));const sun={x:Ww*.8,y:Wh*.2,r:70};return{parts:{clouds,sun},draw(parts,t){ctx.clearRect(0,0,Ww,Wh);/* sun */let g=ctx.createRadialGradient(parts.sun.x,parts.sun.y,10,parts.sun.x,parts.sun.y,parts.sun.r*2);g.addColorStop(0,"rgba(255,209,102,.9)");g.addColorStop(1,"rgba(255,209,102,0)");ctx.fillStyle=g;ctx.beginPath();ctx.arc(parts.sun.x,parts.sun.y,parts.sun.r*2,0,Math.PI*2);ctx.fill();ctx.fillStyle="rgba(255,255,255,.25)";parts.clouds.forEach(c=>{c.x+=c.s;if(c.x-c.r*2>Ww)c.x=-c.r*2;ctx.beginPath();ctx.arc(c.x,c.y,c.r,0,6.283);ctx.arc(c.x+c.r*1.1,c.y+5,c.r*.9,0,6.283);ctx.arc(c.x-c.r*1.1,c.y+8,c.r*.8,0,6.283);ctx.fill();});}}
}if(mode==="rainy"){const drops=[...Array(Math.max(160,Math.floor(Ww*Wh/12000)))].map(()=>({x:rand(0,Ww),y:rand(-Wh,0),l:rand(8,18),v:rand(3,7)}));return{parts:drops,draw(arr,t){ctx.clearRect(0,0,Ww,Wh);ctx.strokeStyle="rgba(140,180,255,.7)";ctx.lineWidth=1;ctx.beginPath();arr.forEach(d=>{ctx.moveTo(d.x,d.y);ctx.lineTo(d.x,d.y+d.l);d.y+=d.v;d.x+=.2; if(d.y>Wh){d.y=rand(-80,0);d.x=rand(0,Ww)}});ctx.stroke();}}
}if(mode==="snowy"){const flakes=[...Array(Math.max(120,Math.floor(Ww*Wh/18000)))].map(()=>({x:rand(0,Ww),y:rand(-Wh,0),r:rand(1.2,3.2),v:rand(.5,1.5),w:rand(.8,1.6),p:rand(0,6.283)}));return{parts:flakes,draw(arr,t){ctx.clearRect(0,0,Ww,Wh);ctx.fillStyle="rgba(255,255,255,.95)";arr.forEach(f=>{f.p+=.01;f.y+=f.v;f.x+=Math.sin(f.p)*f.w; if(f.y>Wh){f.y=rand(-40,0);f.x=rand(0,Ww)};ctx.beginPath();ctx.arc(f.x,f.y,f.r,0,6.283);ctx.fill();});}}
}if(mode==="windy"){const breez=[...Array(Math.max(140,Math.floor(Ww*Wh/15000)))].map(()=>({x:rand(-Ww,0),y:rand(0,Wh),l:rand(8,18),th:rand(.8,1.4),v:rand(2.5,5),a:rand(-.3,.3)}));return{parts:breez,draw(arr,t){ctx.clearRect(0,0,Ww,Wh);ctx.strokeStyle="rgba(123,211,137,.9)";ctx.lineWidth=1.5;ctx.beginPath();arr.forEach(p=>{const dy=Math.sin((p.x+performance.now()/200)*.02)*3;ctx.moveTo(p.x,p.y+dy);ctx.lineTo(p.x+p.l,p.y+dy+p.a*4);p.x+=p.v; if(p.x>Ww+20){p.x=rand(-Ww*0.3,-20);p.y=rand(0,Wh)}});ctx.stroke();}}
}return{parts:[],draw(){ctx.clearRect(0,0,Ww,Wh)}}}
function setMode(mode){if(mode===cur.mode)return;prev={...cur};cur.mode=mode;const built=build(mode);cur.parts=built.parts;cur.draw=built.draw;fadeStart=performance.now();fadeT=0;B.setAttribute("data-mode",mode);descEl.textContent=labels[mode];tempEl.textContent=mode==="snowy"?"-2°C":mode==="rainy"?"18°C":mode==="windy"?"20°C":"25°C";D.querySelectorAll('.controls button').forEach(b=>b.setAttribute('aria-pressed',String(b.dataset.mode===mode)))}
function loop(now){if(!cur.draw){const b=build(cur.mode);cur.parts=b.parts;cur.draw=b.draw}
const t=now; if(prev&&fadeT<1){fadeT=Math.min(1,(now-fadeStart)/fadeDur);ctx.save();ctx.globalAlpha=1-fadeT;prev.draw(prev.parts,t);ctx.restore();ctx.save();ctx.globalAlpha=fadeT;cur.draw(cur.parts,t);ctx.restore();if(fadeT===1)prev=null}else{cur.draw(cur.parts,t)}W.requestAnimationFrame(loop)}W.requestAnimationFrame(loop);
D.querySelectorAll('.controls button').forEach(btn=>btn.addEventListener('click',()=>setMode(btn.dataset.mode)));
})();
</script>
</body></html>